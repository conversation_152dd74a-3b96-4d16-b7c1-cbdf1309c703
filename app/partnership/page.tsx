"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Shield,
  TrendingUp,
  Users,
  DollarSign,
  Star,
  CheckCircle,
  ArrowRight,
  Phone,
  ArrowLeft,
  Target,
  Award,
  Globe,
  Crown,
  AlertCircle,
  X,
  Clock,
} from "lucide-react"
import Link from "next/link"
import {
  getAgentLevels,
  getContactInfo,
  getApplicationStatus,
  submitApplication,
  getPaymentQR,
  checkPaymentStatus,
  startPaymentPolling,
  mockPaymentSuccess,
  type AgentLevel,
  type ContactInfo,
  type ApplicationStatus,
  type ApplicationData
} from "@/lib/partnership-api"
import { isMockPaymentEnabled } from "@/lib/system-settings"

export default function PartnershipPage() {
  const [agentLevels, setAgentLevels] = useState<AgentLevel[]>([])
  const [contactInfo, setContactInfo] = useState<ContactInfo>({ wechat_qr_code: null, phone: null })
  const [applicationStatus, setApplicationStatus] = useState<ApplicationStatus | null>(null)
  const [selectedLevel, setSelectedLevel] = useState<AgentLevel | null>(null)
  const [showApplicationDialog, setShowApplicationDialog] = useState(false)
  const [showPaymentDialog, setShowPaymentDialog] = useState(false)
  const [showSuccessDialog, setShowSuccessDialog] = useState(false)
  const [applicationData, setApplicationData] = useState<ApplicationData>({
    agent_level_id: 0,
    name: "",
    phone: "",
    company: "",
    experience: ""
  })
  const [paymentInfo, setPaymentInfo] = useState<{ qr_code: string; amount: string } | null>(null)
  const [currentApplicationId, setCurrentApplicationId] = useState<number | null>(null)
  const [loading, setLoading] = useState(false)
  const [paymentPolling, setPaymentPolling] = useState(false)
  const [pollingAttempts, setPollingAttempts] = useState(0)
  const [stopPolling, setStopPolling] = useState<(() => void) | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [dataLoading, setDataLoading] = useState(true)
  const [mockPaymentEnabled, setMockPaymentEnabled] = useState(false)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setDataLoading(true)
    try {
      // 先获取代理级别列表
      const levelsResponse = await getAgentLevels()
      let sortedLevels: AgentLevel[] = []
      if (levelsResponse.success) {
        // 按价格排序，价格越高等级越高（价格低的在前面）
        sortedLevels = levelsResponse.data.sort((a: AgentLevel, b: AgentLevel) => {
          const feeA = parseFloat(a.fee || '0')
          const feeB = parseFloat(b.fee || '0')
          return feeA - feeB
        })
        setAgentLevels(sortedLevels)
      }

      // 获取联系方式
      const contactResponse = await getContactInfo(window.location.hostname)
      if (contactResponse.success) {
        setContactInfo(contactResponse.data)
      }

      // 获取申请状态
      const statusResponse = await getApplicationStatus()
      if (statusResponse.success) {
        setApplicationStatus(statusResponse.data)

        // 如果有未付款订单，自动弹出付款弹窗
        if (statusResponse.data.has_pending_application &&
            statusResponse.data.current_application?.payment_status === 'unpaid') {
          const appId = statusResponse.data.current_application.id
          const currentApp = statusResponse.data.current_application

          // 设置当前申请的级别信息，以便在付款弹窗中显示
          if (currentApp.agent_level && sortedLevels.length > 0) {
            // 从已加载的代理级别列表中找到匹配的级别
            const fullLevel = sortedLevels.find(level => level.id === currentApp.agent_level.id)
            if (fullLevel) {
              setSelectedLevel(fullLevel)
            }
          }

          setCurrentApplicationId(appId)
          const paymentResponse = await getPaymentQR(appId)
          if (paymentResponse.success) {
            setPaymentInfo(paymentResponse.data)
            setShowPaymentDialog(true)
          }
        }
      }

      // 获取模拟支付状态
      const mockPaymentStatus = await isMockPaymentEnabled()
      setMockPaymentEnabled(mockPaymentStatus)
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setDataLoading(false)
    }
  }

  const handleLevelSelect = (level: AgentLevel) => {
    // 如果有已付款待审核的申请，不允许选择任何等级
    if (applicationStatus?.has_paid_pending_audit) {
      alert('您有代理申请正在审核中，请等待审核完成后再申请')
      return
    }

    // 检查用户当前代理级别
    if (applicationStatus?.user_agent_level) {
      const currentLevelFee = parseFloat(applicationStatus.user_agent_level.fee || '0')
      const newLevelFee = parseFloat(level.fee)

      // 基于价格判断：不能申请小于等于当前级别价格的代理
      if (newLevelFee <= currentLevelFee) {
        return // 不能选择小于等于当前级别价格的
      }

      // 检查升级费用是否合理
      const upgradeFee = newLevelFee - currentLevelFee
      if (upgradeFee <= 0) {
        alert('升级费用配置错误，请联系管理员')
        return
      }
    }

    setSelectedLevel(level)
    setApplicationData(prev => ({ ...prev, agent_level_id: level.id }))

    // 如果有未完成的申请，加载之前的数据
    if (applicationStatus?.current_application) {
      const currentApp = applicationStatus.current_application
      setApplicationData(prev => ({
        ...prev,
        name: currentApp.name || '',
        phone: currentApp.phone || '',
        company: currentApp.company || '',
        experience: currentApp.experience || ''
      }))
    }

    setShowApplicationDialog(true)
  }

  const handleApplicationSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      const response = await submitApplication(applicationData)
      if (response.success) {
        setCurrentApplicationId(response.data.application_id)
        setShowApplicationDialog(false)

        if (response.data.payment_required) {
          setPaymentInfo({
            qr_code: response.data.qr_code,
            amount: response.data.amount
          })
          setShowPaymentDialog(true)

          // 开始支付状态轮询
          setPaymentPolling(true)
          setPollingAttempts(0)
          const stopFn = startPaymentPolling(
            response.data.application_id,
            (status, attempts) => {
              setPollingAttempts(attempts)
              if (status === 'paid') {
                setPaymentPolling(false)
                setPollingAttempts(0)
                handlePaymentSuccess()
              }
            },
            (error) => {
              console.error('Payment polling error:', error)
              setPaymentPolling(false)
              setPollingAttempts(0)
              setError('支付状态检测失败，请手动刷新页面查看状态')
            },
            () => {
              // 轮询超时
              setPaymentPolling(false)
              setPollingAttempts(0)
              setError('支付状态检测超时，请手动刷新页面查看状态')
            }
          )
          setStopPolling(() => stopFn)
        }
      } else {
        setError('提交申请失败，请重试')
      }
    } catch (error) {
      console.error('Failed to submit application:', error)
      setError('提交申请失败，请检查网络连接后重试')
    } finally {
      setLoading(false)
    }
  }

  const handlePaymentSuccess = async () => {
    setShowPaymentDialog(false)
    setShowSuccessDialog(true)
    // 立即重新加载申请状态，确保界面更新
    await loadData()
  }

  const isLevelDisabled = (level: AgentLevel) => {
    // 如果有已付款待审核的申请，禁用所有等级
    if (applicationStatus?.has_paid_pending_audit) {
      return true
    }

    if (!applicationStatus?.user_agent_level) return false
    // 基于价格判断：不能申请小于等于当前级别价格的代理
    const currentLevelFee = parseFloat(applicationStatus.user_agent_level.fee || '0')
    const targetLevelFee = parseFloat(level.fee || '0')
    return targetLevelFee <= currentLevelFee
  }

  const getLevelCardStyle = (level: AgentLevel) => {
    const baseStyle = "border rounded-xl p-6 transition-colors cursor-pointer flex flex-col h-full"
    if (isLevelDisabled(level)) {
      return `${baseStyle} border-gray-200 opacity-50 cursor-not-allowed`
    }
    return `${baseStyle} border-orange-200 hover:border-orange-400`
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50">
      {/* Header */}
      <header className="border-b border-orange-200/50 bg-white/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  返回首页
                </Button>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl font-bold text-gray-900">招商加盟</h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-orange-200 rounded-full px-6 py-3 mb-6">
            <Users className="h-5 w-5 text-orange-500" />
            <span className="text-sm font-medium text-gray-700">共创AI内容优化新时代</span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            加入
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-red-500">WritePro</span>
            <br />
            合作伙伴计划
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            与我们携手开拓AI内容优化市场，享受丰厚回报和全方位支持，共同打造行业领先的服务生态
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Badge className="bg-orange-100 text-orange-700 border-orange-200 px-4 py-2 text-sm">
              <TrendingUp className="w-4 h-4 mr-2" />
              高成长市场
            </Badge>
            <Badge className="bg-green-100 text-green-700 border-green-200 px-4 py-2 text-sm">
              <DollarSign className="w-4 h-4 mr-2" />
              丰厚利润分成
            </Badge>
            <Badge className="bg-blue-100 text-blue-700 border-blue-200 px-4 py-2 text-sm">
              <Users className="w-4 h-4 mr-2" />
              全程技术支持
            </Badge>
          </div>
        </div>

        {/* 申请状态信息 */}
        {applicationStatus?.current_application && (
          <div className="mb-8">
            {applicationStatus.current_application.payment_status === 'unpaid' && (
              <Alert className="border-yellow-200 bg-yellow-50">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <AlertDescription className="text-yellow-800">
                  <div className="font-medium mb-1">待付款</div>
                  <div>您有一个未付款的代理申请，请完成付款。</div>
                </AlertDescription>
              </Alert>
            )}

            {applicationStatus.current_application.payment_status === 'paid' &&
             applicationStatus.current_application.audit_status === 'pending' && (
              <Alert className="border-blue-200 bg-blue-50">
                <Clock className="h-4 w-4 text-blue-600" />
                <AlertDescription className="text-blue-800">
                  <div className="font-medium mb-1">审核中</div>
                  <div>您的代理申请已提交，正在审核中，请耐心等待。</div>
                </AlertDescription>
              </Alert>
            )}

            {applicationStatus.current_application.audit_status === 'approved' && (
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  <div className="font-medium mb-1">审核通过</div>
                  <div>恭喜！您的代理申请已通过审核，您现在是 {applicationStatus.current_application.agent_level.name}。</div>
                </AlertDescription>
              </Alert>
            )}

            {applicationStatus.current_application.audit_status === 'rejected' && (
              <Alert className="border-red-200 bg-red-50">
                <X className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  <div className="font-medium mb-1">审核未通过</div>
                  <div className="mb-2">很抱歉，您的代理申请未通过审核。</div>
                  {applicationStatus.current_application.audit_reason && (
                    <div className="p-3 bg-red-100 rounded-lg border border-red-200">
                      <div className="font-medium text-sm mb-1">拒绝原因：</div>
                      <div className="text-sm whitespace-pre-wrap">
                        {applicationStatus.current_application.audit_reason}
                      </div>
                    </div>
                  )}
                  <div className="mt-2 text-sm">
                    您可以重新提交申请或联系客服了解详情。
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Partnership Benefits */}
            <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-xl">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                    <Star className="h-5 w-5 text-white" />
                  </div>
                  合作优势
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Target className="h-4 w-4 text-orange-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1">巨大市场潜力</h3>
                        <p className="text-sm text-gray-600">AI内容检测市场年增长率超过40%，万亿级市场等待开发</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Award className="h-4 w-4 text-orange-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1">行业领先技术</h3>
                        <p className="text-sm text-gray-600">99.8%成功率的核心算法，持续技术创新和产品迭代</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <DollarSign className="h-4 w-4 text-orange-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1">丰厚利润回报</h3>
                        <p className="text-sm text-gray-600">最高50%利润分成，多层级奖励机制，月入百万不是梦</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Users className="h-4 w-4 text-orange-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1">全方位支持</h3>
                        <p className="text-sm text-gray-600">专业培训、营销支持、技术指导，助您快速启动业务</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Globe className="h-4 w-4 text-orange-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1">品牌影响力</h3>
                        <p className="text-sm text-gray-600">借助知名品牌影响力，快速建立市场信任和客户基础</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <Shield className="h-4 w-4 text-orange-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1">风险保障</h3>
                        <p className="text-sm text-gray-600">完善的风险控制体系，保障合作伙伴利益和业务稳定</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Agent Levels */}
            <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-xl">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                    <Crown className="h-5 w-5 text-white" />
                  </div>
                  代理级别
                </CardTitle>
              </CardHeader>
              <CardContent>
                {dataLoading ? (
                  /* 加载状态 */
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                    {[1, 2, 3, 4, 5].map((index) => (
                      <div
                        key={index}
                        className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer flex flex-col min-h-[320px]"
                      >
                        {/* 加载动画内容 */}
                        <div className="flex-grow">
                          <div className="w-12 h-12 bg-gray-200 rounded-xl mb-4 animate-pulse"></div>
                          <div className="h-6 bg-gray-200 rounded mb-2 animate-pulse"></div>
                          <div className="space-y-2 mb-4">
                            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                          </div>
                          <div className="space-y-2">
                            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                            <div className="h-3 bg-gray-200 rounded animate-pulse"></div>
                            <div className="h-3 bg-gray-200 rounded animate-pulse"></div>
                            <div className="h-3 bg-gray-200 rounded animate-pulse"></div>
                          </div>
                        </div>

                        {/* 按钮加载动画 */}
                        <div className="flex-shrink-0 mt-4">
                          <div className="w-full h-10 bg-gray-200 rounded animate-pulse flex items-center justify-center">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  /* 正常显示状态 */
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                    {agentLevels.map((level) => (
                      <div
                        key={level.id}
                        className={getLevelCardStyle(level)}
                        onClick={() => !isLevelDisabled(level) && handleLevelSelect(level)}
                      >
                      {/* 卡片内容区域 - 可伸缩 */}
                      <div className="flex-grow">
                        <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center mb-4">
                          <Crown className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="font-bold text-lg text-gray-900 mb-2">{level.name}</h3>
                        <div className="space-y-2 mb-4">
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">代理费：</span>¥{parseFloat(level.fee).toLocaleString()}
                          </p>
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">分成比例：</span>{level.commission_rate}%
                          </p>
                        </div>
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-900">服务内容：</p>
                          <ul className="space-y-1 text-sm text-gray-600">
                            {level.service_content.split('\n').map((service, index) => (
                              <li key={index} className="flex items-center">
                                <CheckCircle className="w-3 h-3 mr-2 text-green-500 flex-shrink-0" />
                                {service}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* 按钮区域 - 固定在底部 */}
                      <div className="flex-shrink-0 mt-4">
                        {!isLevelDisabled(level) && (
                          <Button
                            className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleLevelSelect(level)
                            }}
                          >
                            申请加入
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Button>
                        )}
                        {isLevelDisabled(level) && applicationStatus?.has_paid_pending_audit && (
                          <Badge className="w-full bg-yellow-100 text-yellow-700 border-yellow-200 justify-center">
                            审核中
                          </Badge>
                        )}
                        {isLevelDisabled(level) && !applicationStatus?.has_paid_pending_audit && applicationStatus?.user_agent_level &&
                         parseFloat(applicationStatus.user_agent_level.fee) === parseFloat(level.fee) && (
                          <Badge className="w-full bg-green-100 text-green-700 border-green-200 justify-center">
                            当前级别
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
                )}
              </CardContent>
            </Card>

            {/* Success Stories */}
            <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-xl">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-gray-900">成功案例</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="border-l-4 border-orange-500 pl-6">
                    <h3 className="font-bold text-lg text-gray-900 mb-2">华东区域合作伙伴 - 张总</h3>
                    <p className="text-gray-600 mb-3">
                      "加入WritePro半年来，我们的月收入从10万增长到80万，客户满意度极高。专业的技术支持和完善的培训体系让我们快速上手，现在已经成为当地最大的AI内容优化服务商。"
                    </p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>📈 月收入增长700%</span>
                      <span>👥 服务客户2000+</span>
                      <span>⭐ 客户满意度98%</span>
                    </div>
                  </div>
                  <div className="border-l-4 border-orange-500 pl-6">
                    <h3 className="font-bold text-lg text-gray-900 mb-2">西南区域合作伙伴 - 李总</h3>
                    <p className="text-gray-600 mb-3">
                      "作为教育行业从业者，我深知学术诚信的重要性。WritePro不仅帮助学生提升写作质量，更重要的是培养了他们的原创思维。这是一个有意义且有前景的事业。"
                    </p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>🎓 服务高校50+</span>
                      <span>📚 处理论文10万+</span>
                      <span>💰 年收入500万+</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Contact Us */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8 bg-white/90 backdrop-blur-sm border-0 shadow-xl">
              <CardHeader>
                <CardTitle className="text-xl font-bold text-gray-900 flex items-center gap-2">
                  <Phone className="h-5 w-5 text-orange-500" />
                  联系我们
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 微信二维码 */}
                <div className="text-center">
                  <h4 className="font-semibold text-gray-900 mb-3">微信咨询</h4>
                  <div className="flex justify-center mb-3">
                    {dataLoading ? (
                      <div className="w-32 h-32 bg-gray-100 border border-gray-200 rounded-lg flex items-center justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
                      </div>
                    ) : contactInfo.wechat_qr_code ? (
                      <img
                        src={contactInfo.wechat_qr_code}
                        alt="微信二维码"
                        className="w-32 h-32 border border-gray-200 rounded-lg"
                      />
                    ) : (
                      <div className="w-32 h-32 bg-gray-100 border border-gray-200 rounded-lg flex items-center justify-center">
                        <span className="text-gray-400 text-sm">暂未配置</span>
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-gray-600">扫码添加微信咨询</p>
                </div>

                {/* 电话联系 - 只在有配置时显示 */}
                {contactInfo.phone && (
                  <div className="text-center">
                    <h4 className="font-semibold text-gray-900 mb-3">电话咨询</h4>
                    <div className="flex items-center justify-center">
                      <Phone className="h-4 w-4 mr-2 text-orange-500" />
                      <span className="text-lg font-medium text-gray-900">
                        {contactInfo.phone}
                      </span>
                    </div>
                  </div>
                )}

                {/* 温馨提示 */}
                <div className="p-4 bg-orange-50 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">温馨提示</h4>
                  <ul className="space-y-1 text-sm text-gray-600">
                    <li>• 选择合适的代理级别</li>
                    <li>• 填写真实有效信息</li>
                    <li>• 完成付款后等待审核</li>
                    <li>• 审核通过即可开始代理</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>


      </div>

      {/* 申请表单弹窗 */}
      <Dialog open={showApplicationDialog} onOpenChange={setShowApplicationDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>申请 {selectedLevel?.name}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleApplicationSubmit} className="space-y-4">
            {error && (
              <Alert className="border-red-200 bg-red-50">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-700">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            <div>
              <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                姓名 *
              </Label>
              <Input
                id="name"
                value={applicationData.name}
                onChange={(e) => setApplicationData(prev => ({ ...prev, name: e.target.value }))}
                className="border-orange-200 focus:border-orange-400"
                required
              />
            </div>
            <div>
              <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
                手机 *
              </Label>
              <Input
                id="phone"
                value={applicationData.phone}
                onChange={(e) => setApplicationData(prev => ({ ...prev, phone: e.target.value }))}
                className="border-orange-200 focus:border-orange-400"
                required
              />
            </div>
            <div>
              <Label htmlFor="company" className="text-sm font-medium text-gray-700">
                公司名
              </Label>
              <Input
                id="company"
                value={applicationData.company}
                onChange={(e) => setApplicationData(prev => ({ ...prev, company: e.target.value }))}
                className="border-orange-200 focus:border-orange-400"
              />
            </div>
            <div>
              <Label htmlFor="experience" className="text-sm font-medium text-gray-700">
                相关经验 *
              </Label>
              <Textarea
                id="experience"
                value={applicationData.experience}
                onChange={(e) => setApplicationData(prev => ({ ...prev, experience: e.target.value }))}
                className="border-orange-200 focus:border-orange-400 min-h-[80px]"
                placeholder="请描述您的相关经验..."
                required
              />
            </div>
            <div className="flex gap-3">
              <Button
                type="button"
                variant="outline"
                className="flex-1"
                onClick={() => setShowApplicationDialog(false)}
              >
                取消
              </Button>
              <Button
                type="submit"
                className="flex-1 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white"
                disabled={loading}
              >
                {loading ? "提交中..." : "提交申请"}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* 付款弹窗 */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>扫码支付</DialogTitle>
          </DialogHeader>
          <div className="text-center space-y-4">
            {/* 申请级别信息 */}
            {selectedLevel && (
              <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-200">
                <div className="flex items-center justify-center mb-2">
                  <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center mr-3">
                    <Crown className="h-4 w-4 text-white" />
                  </div>
                  <h3 className="font-bold text-lg text-orange-700">
                    {selectedLevel.name}
                  </h3>
                </div>
                <p className="text-sm text-gray-600">
                  分成比例：<span className="font-medium text-gray-900">{selectedLevel.commission_rate}%</span>
                </p>
              </div>
            )}

            {/* 支付金额 */}
            <div>
              <p className="text-lg font-semibold text-gray-900">
                支付金额：¥{paymentInfo?.amount && parseFloat(paymentInfo.amount).toLocaleString()}
              </p>
            </div>
            {paymentInfo?.qr_code && (
              <div className="flex justify-center">
                <img
                  src={paymentInfo.qr_code}
                  alt="支付二维码"
                  className="w-48 h-48 border border-gray-200 rounded-lg"
                />
              </div>
            )}
            <p className="text-sm text-gray-600">请使用微信或支付宝扫码支付</p>

            {paymentPolling && (
              <div className="flex items-center justify-center space-x-2 text-blue-600">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-sm">
                  正在检测支付状态... ({pollingAttempts}/60)
                </span>
              </div>
            )}

            <div className="space-y-3">
              {/* 测试按钮 - 根据系统配置显示 */}
              {mockPaymentEnabled && (
                <Button
                  className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium"
                  onClick={async () => {
                    console.log('🧪 [测试] 模拟支付成功')
                    // 调用模拟支付成功函数
                    if (currentApplicationId) {
                      try {
                        const success = await mockPaymentSuccess(currentApplicationId)
                        if (success) {
                          console.log('✅ [测试] 支付状态已更新为已付款')
                        } else {
                          console.error('❌ [测试] 模拟支付失败')
                        }
                      } catch (error) {
                        console.error('💥 [测试] 模拟支付异常:', error)
                      }
                    }
                    // 停止轮询
                    if (stopPolling) {
                      stopPolling()
                      setStopPolling(null)
                    }
                    setPaymentPolling(false)
                    setPollingAttempts(0)
                    // 触发支付成功
                    handlePaymentSuccess()
                  }}
                >
                  🧪 模拟支付成功 (测试用)
                </Button>
              )}

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  className="flex-1"
                  onClick={() => {
                    setShowPaymentDialog(false)
                    setPaymentPolling(false)
                    setPollingAttempts(0)
                    if (stopPolling) {
                      stopPolling()
                      setStopPolling(null)
                    }
                  }}
                >
                  取消
                </Button>
                <Button
                  className="flex-1 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white"
                  onClick={handlePaymentSuccess}
                  disabled={paymentPolling}
                >
                  {paymentPolling ? '检测中...' : '已完成支付'}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 成功弹窗 */}
      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>申请已提交</DialogTitle>
          </DialogHeader>
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
            <div>
              <p className="text-lg font-semibold text-gray-900 mb-2">申请提交成功！</p>
              <p className="text-sm text-gray-600">
                您的代理申请已提交，我们将在1-3个工作日内完成审核，请耐心等待。
              </p>
            </div>
            <Button
              className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white"
              onClick={() => setShowSuccessDialog(false)}
            >
              确定
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
